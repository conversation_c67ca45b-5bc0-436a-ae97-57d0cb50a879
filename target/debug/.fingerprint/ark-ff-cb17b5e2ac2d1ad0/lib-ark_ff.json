{"rustc": 15497389221046826682, "features": "[\"default\"]", "declared_features": "[\"asm\", \"default\", \"parallel\", \"rayon\", \"std\"]", "target": 4360302069253712615, "profile": 5347358027863023418, "path": 2649503952403297065, "deps": [[477150410136574819, "ark_ff_macros", false, 15916305488039790875], [2932480923465029663, "zeroize", false, 15513039378270346285], [5157631553186200874, "num_traits", false, 13541603158964661624], [11903278875415370753, "itertools", false, 6221662041904300320], [12528732512569713347, "num_bigint", false, 7237128593493153944], [13859769749131231458, "derivative", false, 14108249347512107688], [15179503056858879355, "ark_std", false, 6043658241629225487], [16925068697324277505, "ark_serialize", false, 11550936738919629962], [17475753849556516473, "digest", false, 8012285272538288596], [17605717126308396068, "paste", false, 7179443179767197851], [17996237327373919127, "ark_ff_asm", false, 7013895505806322940]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/ark-ff-cb17b5e2ac2d1ad0/dep-lib-ark_ff", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}