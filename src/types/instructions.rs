use serde::{Deserialize, Serialize};
use solana_short_vec as short_vec;

/// A compiled instruction
#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, <PERSON><PERSON>, De<PERSON>ult)]
pub struct CompiledInstruction {
    /// Index into the transaction keys array indicating the program account that executes this instruction
    pub program_id_index: u8,
    /// Ordered indices into the transaction keys array indicating which accounts to pass to the program
    #[serde(with = "short_vec")]
    pub accounts: Vec<u8>,
    /// The program input data
    #[serde(with = "short_vec")]
    pub data: Vec<u8>,
}
