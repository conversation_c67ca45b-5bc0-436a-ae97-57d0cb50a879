use serde::{Deserialize, Serialize};
use solana_short_vec as short_vec;

/// A 32-byte public key
#[derive(
    Serialize, Deserialize, Debug, PartialEq, Eq, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ial<PERSON>rd, Ord, Hash,
)]
#[repr(transparent)]
pub struct Pubkey([u8; 32]);

impl Pubkey {
    pub const fn new_from_array(pubkey: [u8; 32]) -> Self {
        Self(pubkey)
    }

    pub fn to_bytes(self) -> [u8; 32] {
        self.0
    }
}

impl AsRef<[u8]> for Pubkey {
    fn as_ref(&self) -> &[u8] {
        &self.0[..]
    }
}

impl From<[u8; 32]> for Pubkey {
    fn from(from: [u8; 32]) -> Self {
        Self(from)
    }
}

/// Address table lookup used to load additional accounts for a transaction
#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, <PERSON><PERSON>, De<PERSON>ult)]
pub struct MessageAddressTableLookup {
    /// Address lookup table account key
    pub account_key: Pubkey,
    /// List of indices used to load writable account addresses
    #[serde(with = "short_vec")]
    pub writable_indexes: Vec<u8>,
    /// List of indices used to load readonly account addresses
    #[serde(with = "short_vec")]
    pub readonly_indexes: Vec<u8>,
}
