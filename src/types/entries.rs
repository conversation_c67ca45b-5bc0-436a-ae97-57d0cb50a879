use serde::{Deserialize, Serialize};
use solana_hash::Hash;

use super::transactions::VersionedTransaction;

/// Custom Entry struct that matches solana-entry exactly
#[derive(Serialize, Deserialize, Debug, Default, PartialEq, Eq, Clone)]
pub struct Entry {
    /// The number of hashes since the previous Entry ID.
    pub num_hashes: u64,
    /// The SHA-256 hash `num_hashes` after the previous Entry ID.
    pub hash: Hash,
    /// An unordered list of transactions that were observed before the Entry ID was generated.
    pub transactions: Vec<VersionedTransaction>,
}
