use serde::{Deserialize, Serialize};
use solana_hash::Hash;
use solana_short_vec as short_vec;

use super::accounts::{MessageAddressTableLookup, Pubkey};
use super::instructions::CompiledInstruction;

/// Custom VersionedMessage enum that matches solana-message exactly
#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone)]
pub enum VersionedMessage {
    Legacy(LegacyMessage),
    V0(V0Message),
}

impl Default for VersionedMessage {
    fn default() -> Self {
        Self::Legacy(LegacyMessage::default())
    }
}

/// Legacy message format
#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default)]
pub struct LegacyMessage {
    /// The message header, identifying signed and read-only account
    pub header: MessageHeader,
    /// All the account keys used by this transaction
    #[serde(with = "short_vec")]
    pub account_keys: Vec<Pubkey>,
    /// The id of a recent ledger entry.
    pub recent_blockhash: Hash,
    /// Programs that will be executed in sequence and committed in one atomic transaction if all succeed.
    #[serde(with = "short_vec")]
    pub instructions: Vec<CompiledInstruction>,
}

/// V0 message format with address lookup tables
#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default)]
pub struct V0Message {
    /// The message header, identifying signed and read-only account
    pub header: MessageHeader,
    /// All the static account keys used by this transaction
    #[serde(with = "short_vec")]
    pub account_keys: Vec<Pubkey>,
    /// The id of a recent ledger entry.
    pub recent_blockhash: Hash,
    /// Programs that will be executed in sequence and committed in one atomic transaction if all succeed.
    #[serde(with = "short_vec")]
    pub instructions: Vec<CompiledInstruction>,
    /// List of address table lookups used to load additional accounts
    #[serde(with = "short_vec")]
    pub address_table_lookups: Vec<MessageAddressTableLookup>,
}

/// The message header, identifying signed and read-only accounts
#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default)]
pub struct MessageHeader {
    /// The number of signatures required for this message to be considered valid.
    pub num_required_signatures: u8,
    /// The last num_readonly_signed_accounts of the signed keys are read-only accounts.
    pub num_readonly_signed_accounts: u8,
    /// The last num_readonly_unsigned_accounts of the unsigned keys are read-only accounts.
    pub num_readonly_unsigned_accounts: u8,
}
