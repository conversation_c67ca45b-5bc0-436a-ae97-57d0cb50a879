use serde::{Deserialize, Serialize};
use solana_short_vec as short_vec;
use solana_signature::Signature;

use super::messages::VersionedMessage;

/// Custom VersionedTransaction that matches solana-transaction exactly
#[derive(Serialize, Deserialize, Debug, <PERSON>ialEq, De<PERSON>ult, Eq, Clone)]
pub struct VersionedTransaction {
    /// List of signatures
    #[serde(with = "short_vec")]
    pub signatures: Vec<Signature>,
    /// Message to sign.
    pub message: VersionedMessage,
}
