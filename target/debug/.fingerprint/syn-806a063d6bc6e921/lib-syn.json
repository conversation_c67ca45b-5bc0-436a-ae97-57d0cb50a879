{"rustc": 15497389221046826682, "features": "[\"clone-impls\", \"default\", \"derive\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 5347358027863023418, "path": 5134834939135515267, "deps": [[1988483478007900009, "unicode_ident", false, 10494539506931360037], [3060637413840920116, "proc_macro2", false, 17760883456097155008], [17990358020177143287, "quote", false, 1633708817241369414]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-806a063d6bc6e921/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}