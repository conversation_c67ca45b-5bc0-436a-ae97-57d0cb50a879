{"rustc": 15497389221046826682, "features": "[\"js\", \"js-sys\", \"wasm-bindgen\"]", "declared_features": "[\"compiler_builtins\", \"core\", \"custom\", \"js\", \"js-sys\", \"linux_disable_fallback\", \"rdrand\", \"rustc-dep-of-std\", \"std\", \"test-in-browser\", \"wasm-bindgen\"]", "target": 16244099637825074703, "profile": 15657897354478470176, "path": 13349453684238134133, "deps": [[6946689283190175495, "wasm_bindgen", false, 10925953871077290866], [9003359908906038687, "js_sys", false, 18232535181687877424], [10411997081178400487, "cfg_if", false, 2665215319541231697]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/debug/.fingerprint/getrandom-89fc599b675f31a8/dep-lib-getrandom", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}