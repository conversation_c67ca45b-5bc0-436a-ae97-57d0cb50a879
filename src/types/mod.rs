//! Custom types that replicate the exact binary serialization format of Solana's official types
//! while using only WASM-compatible dependencies.

pub mod accounts;
pub mod entries;
pub mod instructions;
pub mod messages;
pub mod transactions;

// Re-export all public types for easy access
pub use accounts::{MessageAddressTableLookup, Pubkey};
pub use entries::Entry;
pub use instructions::CompiledInstruction;
pub use messages::{LegacyMessage, MessageHeader, V0Message, VersionedMessage};
pub use transactions::VersionedTransaction;
