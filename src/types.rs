use serde::{Deserialize, Serialize};
use solana_hash::Hash;
use solana_short_vec as short_vec;
use solana_signature::Signature;

/// Custom Entry struct that matches solana-entry exactly
#[derive(Serialize, Deserialize, Debug, Default, PartialEq, Eq, Clone)]
pub struct Entry {
    /// The number of hashes since the previous Entry ID.
    pub num_hashes: u64,
    /// The SHA-256 hash `num_hashes` after the previous Entry ID.
    pub hash: Hash,
    /// An unordered list of transactions that were observed before the Entry ID was generated.
    pub transactions: Vec<VersionedTransaction>,
}

/// Custom VersionedTransaction that matches solana-transaction exactly
#[derive(Serialize, Deserialize, Debug, PartialEq, Default, Eq, Clone)]
pub struct VersionedTransaction {
    /// List of signatures
    #[serde(with = "short_vec")]
    pub signatures: Vec<Signature>,
    /// Message to sign.
    pub message: VersionedMessage,
}

/// Custom VersionedMessage enum that matches solana-message exactly
#[derive(Serialize, Deserialize, Debug, <PERSON>ial<PERSON>q, Eq, <PERSON>lone)]
pub enum VersionedMessage {
    Legacy(LegacyMessage),
    V0(V0Message),
}

impl Default for VersionedMessage {
    fn default() -> Self {
        Self::Legacy(LegacyMessage::default())
    }
}

/// Legacy message format
#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default)]
pub struct LegacyMessage {
    /// The message header, identifying signed and read-only account
    pub header: MessageHeader,
    /// All the account keys used by this transaction
    #[serde(with = "short_vec")]
    pub account_keys: Vec<Pubkey>,
    /// The id of a recent ledger entry.
    pub recent_blockhash: Hash,
    /// Programs that will be executed in sequence and committed in one atomic transaction if all succeed.
    #[serde(with = "short_vec")]
    pub instructions: Vec<CompiledInstruction>,
}

/// V0 message format with address lookup tables
#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default)]
pub struct V0Message {
    /// The message header, identifying signed and read-only account
    pub header: MessageHeader,
    /// All the static account keys used by this transaction
    #[serde(with = "short_vec")]
    pub account_keys: Vec<Pubkey>,
    /// The id of a recent ledger entry.
    pub recent_blockhash: Hash,
    /// Programs that will be executed in sequence and committed in one atomic transaction if all succeed.
    #[serde(with = "short_vec")]
    pub instructions: Vec<CompiledInstruction>,
    /// List of address table lookups used to load additional accounts
    #[serde(with = "short_vec")]
    pub address_table_lookups: Vec<MessageAddressTableLookup>,
}

/// The message header, identifying signed and read-only accounts
#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default)]
pub struct MessageHeader {
    /// The number of signatures required for this message to be considered valid.
    pub num_required_signatures: u8,
    /// The last num_readonly_signed_accounts of the signed keys are read-only accounts.
    pub num_readonly_signed_accounts: u8,
    /// The last num_readonly_unsigned_accounts of the unsigned keys are read-only accounts.
    pub num_readonly_unsigned_accounts: u8,
}

/// A 32-byte public key
#[derive(
    Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Copy, Default, PartialOrd, Ord, Hash,
)]
#[repr(transparent)]
pub struct Pubkey([u8; 32]);

impl Pubkey {
    pub const fn new_from_array(pubkey: [u8; 32]) -> Self {
        Self(pubkey)
    }

    pub fn to_bytes(self) -> [u8; 32] {
        self.0
    }
}

impl AsRef<[u8]> for Pubkey {
    fn as_ref(&self) -> &[u8] {
        &self.0[..]
    }
}

impl From<[u8; 32]> for Pubkey {
    fn from(from: [u8; 32]) -> Self {
        Self(from)
    }
}

/// A compiled instruction
#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default)]
pub struct CompiledInstruction {
    /// Index into the transaction keys array indicating the program account that executes this instruction
    pub program_id_index: u8,
    /// Ordered indices into the transaction keys array indicating which accounts to pass to the program
    #[serde(with = "short_vec")]
    pub accounts: Vec<u8>,
    /// The program input data
    #[serde(with = "short_vec")]
    pub data: Vec<u8>,
}

/// Address table lookup used to load additional accounts for a transaction
#[derive(Serialize, Deserialize, Debug, PartialEq, Eq, Clone, Default)]
pub struct MessageAddressTableLookup {
    /// Address lookup table account key
    pub account_key: Pubkey,
    /// List of indices used to load writable account addresses
    #[serde(with = "short_vec")]
    pub writable_indexes: Vec<u8>,
    /// List of indices used to load readonly account addresses
    #[serde(with = "short_vec")]
    pub readonly_indexes: Vec<u8>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_entry_basic_serialization() {
        // Create a simple test entry with our custom types
        let custom_entry = Entry {
            num_hashes: 42,
            hash: Hash::new_from_array([1u8; 32]),
            transactions: vec![],
        };

        // Serialize and deserialize with our custom implementation
        let custom_bytes = bincode::serialize(&custom_entry).unwrap();
        let deserialized: Entry = bincode::deserialize(&custom_bytes).unwrap();

        assert_eq!(custom_entry.num_hashes, deserialized.num_hashes);
        assert_eq!(custom_entry.hash.to_bytes(), deserialized.hash.to_bytes());
        assert_eq!(
            custom_entry.transactions.len(),
            deserialized.transactions.len()
        );
    }

    #[test]
    fn test_versioned_transaction_empty_serialization() {
        // Test empty VersionedTransaction
        let custom_tx = VersionedTransaction {
            signatures: vec![],
            message: VersionedMessage::Legacy(LegacyMessage::default()),
        };

        let custom_bytes = bincode::serialize(&custom_tx).unwrap();

        // Verify we can deserialize it back
        let deserialized: VersionedTransaction = bincode::deserialize(&custom_bytes).unwrap();
        assert_eq!(custom_tx, deserialized);
    }

    #[test]
    fn test_hash_compatibility() {
        let test_bytes = [42u8; 32];

        // Test our Hash type
        let custom_hash = Hash::new_from_array(test_bytes);
        let custom_serialized = bincode::serialize(&custom_hash).unwrap();

        // Test official Hash type
        let official_hash = solana_hash::Hash::new_from_array(test_bytes);
        let official_serialized = bincode::serialize(&official_hash).unwrap();

        // Should be identical
        assert_eq!(
            custom_serialized, official_serialized,
            "Hash serialization mismatch"
        );

        // Cross-deserialization should work
        let custom_from_official: Hash = bincode::deserialize(&official_serialized).unwrap();
        let official_from_custom: solana_hash::Hash =
            bincode::deserialize(&custom_serialized).unwrap();

        assert_eq!(custom_from_official.to_bytes(), test_bytes);
        assert_eq!(official_from_custom.to_bytes(), test_bytes);
    }

    #[test]
    fn test_signature_compatibility() {
        let test_bytes = [42u8; 64];

        // Test our Signature type
        let custom_sig = Signature::from(test_bytes);
        let custom_serialized = bincode::serialize(&custom_sig).unwrap();

        // Test official Signature type
        let official_sig = solana_signature::Signature::from(test_bytes);
        let official_serialized = bincode::serialize(&official_sig).unwrap();

        // Should be identical
        assert_eq!(
            custom_serialized, official_serialized,
            "Signature serialization mismatch"
        );

        // Cross-deserialization should work
        let custom_from_official: Signature = bincode::deserialize(&official_serialized).unwrap();
        let official_from_custom: solana_signature::Signature =
            bincode::deserialize(&custom_serialized).unwrap();

        assert_eq!(custom_from_official.as_ref(), test_bytes.as_ref());
        assert_eq!(official_from_custom.as_ref(), test_bytes.as_ref());
    }

    #[test]
    fn test_entry_with_transaction_serialization() {
        // Create a more complex entry with a transaction
        let signature = Signature::from([1u8; 64]);
        let pubkey = Pubkey::from([2u8; 32]);
        let hash = Hash::new_from_array([3u8; 32]);

        let legacy_message = LegacyMessage {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 1,
            },
            account_keys: vec![pubkey],
            recent_blockhash: hash,
            instructions: vec![CompiledInstruction {
                program_id_index: 0,
                accounts: vec![0],
                data: vec![1, 2, 3],
            }],
        };

        let versioned_tx = VersionedTransaction {
            signatures: vec![signature],
            message: VersionedMessage::Legacy(legacy_message),
        };

        let entry = Entry {
            num_hashes: 100,
            hash,
            transactions: vec![versioned_tx],
        };

        // Test serialization/deserialization
        let serialized = bincode::serialize(&entry).unwrap();
        let deserialized: Entry = bincode::deserialize(&serialized).unwrap();

        assert_eq!(entry.num_hashes, deserialized.num_hashes);
        assert_eq!(entry.hash.to_bytes(), deserialized.hash.to_bytes());
        assert_eq!(entry.transactions.len(), deserialized.transactions.len());

        // Verify transaction details
        let tx = &deserialized.transactions[0];
        assert_eq!(tx.signatures.len(), 1);
        assert_eq!(tx.signatures[0].as_ref(), [1u8; 64].as_ref());

        if let VersionedMessage::Legacy(msg) = &tx.message {
            assert_eq!(msg.header.num_required_signatures, 1);
            assert_eq!(msg.account_keys.len(), 1);
            assert_eq!(msg.instructions.len(), 1);
            assert_eq!(msg.instructions[0].data, vec![1, 2, 3]);
        } else {
            panic!("Expected Legacy message");
        }
    }

    #[test]
    fn test_v0_message_serialization() {
        let pubkey = Pubkey::from([4u8; 32]);
        let hash = Hash::new_from_array([5u8; 32]);

        let v0_message = V0Message {
            header: MessageHeader {
                num_required_signatures: 1,
                num_readonly_signed_accounts: 0,
                num_readonly_unsigned_accounts: 2,
            },
            account_keys: vec![pubkey],
            recent_blockhash: hash,
            instructions: vec![],
            address_table_lookups: vec![MessageAddressTableLookup {
                account_key: pubkey,
                writable_indexes: vec![0, 1],
                readonly_indexes: vec![2, 3],
            }],
        };

        let versioned_tx = VersionedTransaction {
            signatures: vec![],
            message: VersionedMessage::V0(v0_message),
        };

        // Test serialization/deserialization
        let serialized = bincode::serialize(&versioned_tx).unwrap();
        let deserialized: VersionedTransaction = bincode::deserialize(&serialized).unwrap();

        if let VersionedMessage::V0(msg) = &deserialized.message {
            assert_eq!(msg.header.num_readonly_unsigned_accounts, 2);
            assert_eq!(msg.address_table_lookups.len(), 1);
            assert_eq!(msg.address_table_lookups[0].writable_indexes, vec![0, 1]);
            assert_eq!(msg.address_table_lookups[0].readonly_indexes, vec![2, 3]);
        } else {
            panic!("Expected V0 message");
        }
    }
}
