{"rustc": 15497389221046826682, "features": "[\"default\", \"msrv\", \"rustversion\", \"std\"]", "declared_features": "[\"default\", \"enable-interning\", \"gg-alloc\", \"msrv\", \"rustversion\", \"serde\", \"serde-serialize\", \"serde_json\", \"spans\", \"std\", \"strict-macro\", \"xxx_debug_only_print_generated_code\"]", "target": 4070942113156591848, "profile": 6374401459973044251, "path": 1545970865074875010, "deps": [[3722963349756955755, "once_cell", false, 1416689770263163194], [6946689283190175495, "build_script_build", false, 1510510523207344255], [7858942147296547339, "rustversion", false, 13058162117144251068], [10411997081178400487, "cfg_if", false, 2665215319541231697], [11382113702854245495, "wasm_bindgen_macro", false, 3254278886134720950]], "local": [{"CheckDepInfo": {"dep_info": "wasm32-unknown-unknown/debug/.fingerprint/wasm-bindgen-46fdebf625e14f3e/dep-lib-wasm_bindgen", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 14682669768258224367}