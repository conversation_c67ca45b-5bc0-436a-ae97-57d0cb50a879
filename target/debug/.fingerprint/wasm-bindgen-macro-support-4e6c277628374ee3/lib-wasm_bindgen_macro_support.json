{"rustc": 15497389221046826682, "features": "[]", "declared_features": "[\"extra-traits\", \"strict-macro\"]", "target": 17930477452216118438, "profile": 11461959714804267394, "path": 10215168194706528797, "deps": [[3060637413840920116, "proc_macro2", false, 17760883456097155008], [14299170049494554845, "wasm_bindgen_shared", false, 5640608280205634445], [14372503175394433084, "wasm_bindgen_backend", false, 13606942317252808344], [17990358020177143287, "quote", false, 1633708817241369414], [18149961000318489080, "syn", false, 11510011774250661250]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/wasm-bindgen-macro-support-4e6c277628374ee3/dep-lib-wasm_bindgen_macro_support", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}