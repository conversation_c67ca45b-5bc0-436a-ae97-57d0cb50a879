{"$message_type":"diagnostic","message":"unused imports: `MessageAddressTableLookup` and `Pubkey`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/types/mod.rs","byte_start":312,"byte_end":337,"line_start":11,"line_end":11,"column_start":20,"column_end":45,"is_primary":true,"text":[{"text":"pub use accounts::{MessageAddressTableLookup, Pubkey};","highlight_start":20,"highlight_end":45}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/types/mod.rs","byte_start":339,"byte_end":345,"line_start":11,"line_end":11,"column_start":47,"column_end":53,"is_primary":true,"text":[{"text":"pub use accounts::{MessageAddressTableLookup, <PERSON><PERSON>};","highlight_start":47,"highlight_end":53}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(unused_imports)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/types/mod.rs","byte_start":293,"byte_end":348,"line_start":11,"line_end":12,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use accounts::{MessageAddressTableLookup, Pubkey};","highlight_start":1,"highlight_end":55},{"text":"pub use entries::Entry;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `MessageAddressTableLookup` and `Pubkey`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/types/mod.rs:11:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use accounts::{MessageAddressTableLookup, Pubkey};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(unused_imports)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `instructions::CompiledInstruction`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/types/mod.rs","byte_start":380,"byte_end":413,"line_start":13,"line_end":13,"column_start":9,"column_end":42,"is_primary":true,"text":[{"text":"pub use instructions::CompiledInstruction;","highlight_start":9,"highlight_end":42}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/types/mod.rs","byte_start":372,"byte_end":415,"line_start":13,"line_end":14,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use instructions::CompiledInstruction;","highlight_start":1,"highlight_end":43},{"text":"pub use messages::{LegacyMessage, MessageHeader, V0Message, VersionedMessage};","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `instructions::CompiledInstruction`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/types/mod.rs:13:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m13\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use instructions::CompiledInstruction;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused imports: `LegacyMessage`, `MessageHeader`, `V0Message`, and `VersionedMessage`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/types/mod.rs","byte_start":434,"byte_end":447,"line_start":14,"line_end":14,"column_start":20,"column_end":33,"is_primary":true,"text":[{"text":"pub use messages::{LegacyMessage, MessageHeader, V0Message, VersionedMessage};","highlight_start":20,"highlight_end":33}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/types/mod.rs","byte_start":449,"byte_end":462,"line_start":14,"line_end":14,"column_start":35,"column_end":48,"is_primary":true,"text":[{"text":"pub use messages::{LegacyMessage, MessageHeader, V0Message, VersionedMessage};","highlight_start":35,"highlight_end":48}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/types/mod.rs","byte_start":464,"byte_end":473,"line_start":14,"line_end":14,"column_start":50,"column_end":59,"is_primary":true,"text":[{"text":"pub use messages::{LegacyMessage, MessageHeader, V0Message, VersionedMessage};","highlight_start":50,"highlight_end":59}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/types/mod.rs","byte_start":475,"byte_end":491,"line_start":14,"line_end":14,"column_start":61,"column_end":77,"is_primary":true,"text":[{"text":"pub use messages::{LegacyMessage, MessageHeader, V0Message, VersionedMessage};","highlight_start":61,"highlight_end":77}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/types/mod.rs","byte_start":415,"byte_end":494,"line_start":14,"line_end":15,"column_start":1,"column_end":1,"is_primary":true,"text":[{"text":"pub use messages::{LegacyMessage, MessageHeader, V0Message, VersionedMessage};","highlight_start":1,"highlight_end":79},{"text":"pub use transactions::VersionedTransaction;","highlight_start":1,"highlight_end":1}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused imports: `LegacyMessage`, `MessageHeader`, `V0Message`, and `VersionedMessage`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/types/mod.rs:14:20\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m14\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use messages::{LegacyMessage, MessageHeader, V0Message, VersionedMessage};\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                    \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"unused import: `transactions::VersionedTransaction`","code":{"code":"unused_imports","explanation":null},"level":"warning","spans":[{"file_name":"src/types/mod.rs","byte_start":502,"byte_end":536,"line_start":15,"line_end":15,"column_start":9,"column_end":43,"is_primary":true,"text":[{"text":"pub use transactions::VersionedTransaction;","highlight_start":9,"highlight_end":43}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"remove the whole `use` item","code":null,"level":"help","spans":[{"file_name":"src/types/mod.rs","byte_start":494,"byte_end":538,"line_start":15,"line_end":15,"column_start":1,"column_end":45,"is_primary":true,"text":[{"text":"pub use transactions::VersionedTransaction;","highlight_start":1,"highlight_end":45}],"label":null,"suggested_replacement":"","suggestion_applicability":"MachineApplicable","expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: unused import: `transactions::VersionedTransaction`\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/types/mod.rs:15:9\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m15\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mpub use transactions::VersionedTransaction;\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m         \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"associated items `new_from_array` and `to_bytes` are never used","code":{"code":"dead_code","explanation":null},"level":"warning","spans":[{"file_name":"src/types/accounts.rs","byte_start":257,"byte_end":268,"line_start":11,"line_end":11,"column_start":1,"column_end":12,"is_primary":false,"text":[{"text":"impl Pubkey {","highlight_start":1,"highlight_end":12}],"label":"associated items in this implementation","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/types/accounts.rs","byte_start":288,"byte_end":302,"line_start":12,"line_end":12,"column_start":18,"column_end":32,"is_primary":true,"text":[{"text":"    pub const fn new_from_array(pubkey: [u8; 32]) -> Self {","highlight_start":18,"highlight_end":32}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src/types/accounts.rs","byte_start":370,"byte_end":378,"line_start":16,"line_end":16,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn to_bytes(self) -> [u8; 32] {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"`#[warn(dead_code)]` on by default","code":null,"level":"note","spans":[],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: associated items `new_from_array` and `to_bytes` are never used\u001b[0m\n\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m--> \u001b[0m\u001b[0msrc/types/accounts.rs:12:18\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m11\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0mimpl Pubkey {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m-----------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12massociated items in this implementation\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m12\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub const fn new_from_array(pubkey: [u8; 32]) -> Self {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m                  \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^^^^^^^\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m...\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;12m16\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn to_bytes(self) -> [u8; 32] {\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[33m^^^^^^^^\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m|\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;12m= \u001b[0m\u001b[0m\u001b[1mnote\u001b[0m\u001b[0m: `#[warn(dead_code)]` on by default\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"5 warnings emitted","code":null,"level":"warning","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[33mwarning\u001b[0m\u001b[0m\u001b[1m: 5 warnings emitted\u001b[0m\n\n"}
