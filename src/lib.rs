mod types;

use types::Entry;
use wasm_bindgen::prelude::*;

#[wasm_bindgen]
pub fn decode_entries(bytes: &[u8]) -> Result<JsValue, JsValue> {
    let v: Vec<Entry> = bincode::deserialize(bytes)
        .map_err(|e| JsValue::from_str(&format!("Failed to deserialize entries: {}", e)))?;

    serde_wasm_bindgen::to_value(&v)
        .map_err(|e| JsValue::from_str(&format!("Failed to convert to JsValue: {}", e)))
}
